using GPUAnimationCrowds;
using MonoToECSShadow.Components;
using PlayerFAP.Components;
using ProjectDawn.Navigation;
using Unity.Burst;
using Unity.Burst.Intrinsics;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace Systems.PlayerFAP
{
    [BurstCompile]
    [UpdateAfter(typeof(AgentForceSystem))]
    public partial struct EnemyMovementSystem : ISystem
    {
        private const float DESTINATION_UPDATE_TOLERANCE_SQ = 0.1f * 0.1f;
        // Threshold no longer needed – movement decided by AgentBody.IsStopped flag

        private float3 m_lastPlayerPosition;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EnemyTag>();
            state.RequireForUpdate<PlayerTransform>();
            state.RequireForUpdate<AgentBody>();
            state.RequireForUpdate<CharacterMovementState>();
            m_lastPlayerPosition = new float3(float.MinValue);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            float3 playerPosition = float3.zero;
            if (!SystemAPI.TryGetSingleton<PlayerTransform>(out var playerTransform))
            {
                return;
            }
            playerPosition = playerTransform.Position;

            // Ensure every enemy is always heading toward the current player position.
            // We update only when the stored destination differs enough from the player to avoid
            // excessive structural changes and cache misses.
            foreach (var destination in SystemAPI.Query<RefRW<SetDestination>>().WithAll<EnemyTag>())
            {
                if (math.distancesq(destination.ValueRO.Value, playerPosition) > DESTINATION_UPDATE_TOLERANCE_SQ)
                {
                    destination.ValueRW.Value = playerPosition;
                }
            }

            // Track for possible future system-level optimisations
            m_lastPlayerPosition = playerPosition;

            foreach (var (agentBody, agentCollider, movementState) in
                     SystemAPI.Query<RefRW<AgentBody>, EnabledRefRW<AgentCollider>, RefRW<CharacterMovementState>>()
                         .WithAll<EnemyTag>())
            {
                // Navigation package sets Force to zero the exact frame the agent has arrived.
                bool shouldStop = math.lengthsq(agentBody.ValueRO.Force) < 1e-6f;
                agentBody.ValueRW.IsStopped = shouldStop;

                float currentSpeed = math.length(agentBody.ValueRO.Velocity);
                movementState.ValueRW.Speed = currentSpeed;
                movementState.ValueRW.IsMoving = !shouldStop;

                if (!movementState.ValueRO.IsMoving)
                {
                    //the Agent Collider must be diable when fully stop
                }
                else
                {
                    //the Agent Collider must be enable when start to move
                }
                
            }
        }
    }

    [BurstCompile]
    public struct SetDestinationJob : IJobChunk
    {
        public ComponentTypeHandle<AgentBody> AgentBodyTypeHandle;
        [ReadOnly] public ComponentTypeHandle<AgentBody> SetDestinationTypeHandle;

        public float3 playerPosition;
        
        public void Execute([ChunkIndexInQuery] in ArchetypeChunk chunk, int unfilteredChunkIndex, bool useEnabledMask, in v128 chunkEnabledMask)
        {
            var agentBodies = chunk.GetNativeArray(AgentBodyTypeHandle);
            var destinations = chunk.GetNativeArray(SetDestinationTypeHandle);

            for (int i = 0; i < chunk.Count; i++)
            {
                var agentBody = agentBodies[i];
                var destination = destinations[i];

                agentBody.SetDestination(playerPosition);

                agentBodies[i] = agentBody;
            }
        }
    }

}