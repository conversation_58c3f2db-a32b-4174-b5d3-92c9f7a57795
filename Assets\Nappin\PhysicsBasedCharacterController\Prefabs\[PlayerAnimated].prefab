%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &468307785411360689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 468307785411360694}
  m_Layer: 8
  m_Name: Mesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &468307785411360694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307785411360689}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 8324388390880657228}
  m_Father: {fileID: 468307785458387721}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &468307785458387723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 468307785458387721}
  - component: {fileID: 468307785458387727}
  - component: {fileID: 468307785458387720}
  - component: {fileID: 468307785458387724}
  m_Layer: 8
  m_Name: '[PlayerAnimated]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &468307785458387721
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307785458387723}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -80.145, y: 13.21, z: -59.289528}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 468307785411360694}
  - {fileID: 468307786512677164}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &468307785458387727
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307785458387723}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &468307785458387720
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307785458387723}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 112
  m_CollisionDetection: 0
--- !u!114 &468307785458387724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307785458387723}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d653000bcba62c14ba9b6c5c324a4197, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  groundMask:
    serializedVersion: 2
    m_Bits: 512
  movementSpeed: 14.4
  movementThrashold: 0.01
  dampSpeedUp: 0.2
  dampSpeedDown: 0.1
  jumpVelocity: 20
  fallMultiplier: 1.7
  holdJumpMultiplier: 5
  frictionAgainstFloor: 0.189
  frictionAgainstWall: 0.082
  canLongJump: 1
  groundCheckerThrashold: 0.1
  slopeCheckerThrashold: 0.51
  stepCheckerThrashold: 0.6
  maxClimbableSlopeAngle: 53.6
  maxStepHeight: 0.74
  speedMultiplierOnAngle:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  canSlideMultiplierCurve: 0.061
  cantSlideMultiplierCurve: 0.039
  climbingStairsMultiplierCurve: 0.086
  gravityMultiplier: 8
  gravityMultiplyerOnSlideChange: 2
  gravityMultiplierIfUnclimbableSlope: 30
  lockOnSlope: 1
  wallCheckerThrashold: 0.8
  hightWallCheckerChecker: 0.5
  jumpFromWallMultiplier: 31
  multiplierVerticalLeap: 1
  sprintSpeed: 20
  crouchHeightMultiplier: 0.5
  POV_normalHeadHeight: {x: 0, y: 0.5, z: -0.1}
  POV_crouchHeadHeight: {x: 0, y: -0.1, z: -0.1}
  characterCamera: {fileID: 0}
  characterModel: {fileID: 468307785411360689}
  characterModelRotationSmooth: 0.1
  meshCharacter: {fileID: 8704890248664180214}
  meshCharacterCrouch: {fileID: 0}
  headPoint: {fileID: 468307786512677164}
  input: {fileID: 0}
  debug: 1
  OnJump:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleJump
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  minimumVerticalSpeedToLandEvent: 0.5
  OnLand:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleLand
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  minimumHorizontalSpeedToFastEvent: 20
  OnFast:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_MethodName: ParticleFast
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnWallSlide:
    m_PersistentCalls:
      m_Calls: []
  OnSprint:
    m_PersistentCalls:
      m_Calls: []
  OnCrouch:
    m_PersistentCalls:
      m_Calls: []
  targetAngle: 47.939434
--- !u!1 &468307786512677167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 468307786512677164}
  m_Layer: 8
  m_Name: HeadPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &468307786512677164
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468307786512677167}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: -0.1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 468307785458387721}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2919776601649508381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8704890248664180214}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6401a0300cc05d74a953f65bf864fd51, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  characterManager: {fileID: 468307785458387724}
  rigidbodyCharacter: {fileID: 468307785458387720}
  velocityAnimationMultiplier: 1
  lockRotationOnWall: 1
  climbThreshold: 0.5
--- !u!1001 &6858583229637946395
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 468307785411360694}
    m_Modifications:
    - target: {fileID: 2874262570760833517, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_Name
      value: characterAnimated
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.001
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 8714b7f0b38d36540b1b8396757bea0c, type: 3}
--- !u!1 &8704890248664180214 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2874262570760833517, guid: 8714b7f0b38d36540b1b8396757bea0c,
    type: 3}
  m_PrefabInstance: {fileID: 6858583229637946395}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &8324388390880657228 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3218031085708825431, guid: 8714b7f0b38d36540b1b8396757bea0c,
    type: 3}
  m_PrefabInstance: {fileID: 6858583229637946395}
  m_PrefabAsset: {fileID: 0}
