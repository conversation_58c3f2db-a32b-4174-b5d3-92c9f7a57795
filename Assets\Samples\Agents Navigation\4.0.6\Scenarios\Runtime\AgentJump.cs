using ProjectDawn.Navigation.Hybrid;
using Unity.Mathematics;
using UnityEngine;

public class AgentJump : MonoBehaviour
{
    public float Duration = 1.0f;

    AgentNavMeshAuthoring m_NavMesh;
    bool m_Jumping;
    double m_JumpTimeStamp;

    void Start()
    {
        m_NavMesh = GetComponent<AgentNavMeshAuthoring>();
    }

    void Update()
    {
        // Skip, if not on active link
        if (!m_NavMesh.OnLinkTraversal)
            return;

        if (!m_Jumping)
        {
            // Start jumping sequence
            m_JumpTimeStamp = Time.realtimeSinceStartupAsDouble;
            m_Jumping = true;
        }
        else
        {
            float timeInAir = (float)((Time.realtimeSinceStartupAsDouble - m_JumpTimeStamp) / Duration);

            if (timeInAir > Duration)
            {
                // Finish link traversal
                m_NavMesh.OnLinkTraversal = false;
                m_Jumping = false;
            }
            else
            {
                // Do nice parabolic jump
                float progress = math.saturate(timeInAir / Duration);
                var seek = m_NavMesh.NavMeshLinkTraversal.Seek;
                float3 start = seek.Start.Left;
                float3 end = seek.End.GetClosestPortalPoint(transform.position);
                transform.position = ParabolicLerp(start, end, progress);
            }
        }
    }

    // Generated by chatgpt
    static Vector3 ParabolicLerp(Vector3 pointA, Vector3 pointB, float lerpValue)
    {
        // Ensure lerpValue is clamped between 0 and 1
        lerpValue = Mathf.Clamp01(lerpValue);

        // Calculate the control point position
        Vector3 controlPoint = CalculateControlPoint(pointA, pointB);

        // Perform quadratic Bezier interpolation
        return QuadraticBezier(pointA, controlPoint, pointB, lerpValue);
    }

    // Generated by chatgpt
    // Calculate the control point for quadratic Bezier curve
    static Vector3 CalculateControlPoint(Vector3 pointA, Vector3 pointB)
    {
        // Calculate the mid-point between pointA and pointB
        Vector3 midPoint = (pointA + pointB) / 2f;

        // Adjust the y-coordinate of the mid-point to form a parabolic curve
        midPoint.y = Mathf.Max(pointA.y, pointB.y) + 0.5f;

        return midPoint;
    }

    // Generated by chatgpt
    // Perform quadratic Bezier interpolation
    static Vector3 QuadraticBezier(Vector3 pointA, Vector3 controlPoint, Vector3 pointB, float t)
    {
        float u = 1f - t;
        float tt = t * t;
        float uu = u * u;

        Vector3 result = uu * pointA;
        result += 2 * u * t * controlPoint;
        result += tt * pointB;

        return result;
    }
}
