%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &940345403289609729
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403289609756}
  - component: {fileID: 940345403289609757}
  - component: {fileID: 940345403289609758}
  - component: {fileID: 940345403289609728}
  - component: {fileID: 940345403289609753}
  - component: {fileID: 940345403289609754}
  - component: {fileID: 940345403289609755}
  m_Layer: 9
  m_Name: Connector_11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403289609756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 2.1699905, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345403289609757
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403289609758
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345403289609728
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345403289609753
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345403289609754
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_ConnectedBody: {fileID: 940345403689339917}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.10309317, z: 0.93170947}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345403289609755
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403289609729}
  m_ConnectedBody: {fileID: 940345404732579905}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.19511874}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345403353457488
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403353457515}
  - component: {fileID: 940345403353457516}
  - component: {fileID: 940345403353457517}
  - component: {fileID: 940345403353457519}
  - component: {fileID: 940345403353457512}
  - component: {fileID: 940345403353457513}
  - component: {fileID: 940345403353457514}
  m_Layer: 9
  m_Name: Connector_12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403353457515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 2.1699905, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345403353457516
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403353457517
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345403353457519
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345403353457512
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345403353457513
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_ConnectedBody: {fileID: 940345403689339917}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.10309317, z: 0.9317089}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345403353457514
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403353457488}
  m_ConnectedBody: {fileID: 940345404732579905}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.19511922}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345403371841177
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403371841169}
  - component: {fileID: 940345403371841170}
  - component: {fileID: 940345403371841171}
  - component: {fileID: 940345403371841173}
  - component: {fileID: 940345403371841174}
  - component: {fileID: 940345403371841175}
  - component: {fileID: 940345403371841176}
  m_Layer: 9
  m_Name: Connector_31
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403371841169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 6.6299973, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345403371841170
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403371841171
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345403371841173
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345403371841174
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345403371841175
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_ConnectedBody: {fileID: 940345403772874516}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.89268416}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345403371841176
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403371841177}
  m_ConnectedBody: {fileID: 940345404381596550}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.17560983}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345403410411106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403410411132}
  - component: {fileID: 940345403410411133}
  - component: {fileID: 940345403410411134}
  - component: {fileID: 940345403410411105}
  - component: {fileID: 940345403410411104}
  m_Layer: 9
  m_Name: Plank_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403410411132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403410411106}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 9.859993, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345403410411133
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403410411106}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403410411134
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403410411106}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345403410411105
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403410411106}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345403410411104
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403410411106}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000034332275}
--- !u!1 &940345403689340019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403689339918}
  - component: {fileID: 940345403689339919}
  - component: {fileID: 940345403689340016}
  - component: {fileID: 940345403689339917}
  - component: {fileID: 940345403689340018}
  m_Layer: 9
  m_Name: Plank_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403689339918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403689340019}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0.91999054, y: -0.020000458, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345403689339919
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403689340019}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403689340016
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403689340019}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345403689339917
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403689340019}
  serializedVersion: 2
  m_Mass: 5
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 126
  m_CollisionDetection: 0
--- !u!65 &940345403689340018
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403689340019}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000034332275}
--- !u!1 &940345403772874517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345403772874543}
  - component: {fileID: 940345403772874512}
  - component: {fileID: 940345403772874513}
  - component: {fileID: 940345403772874516}
  - component: {fileID: 940345403772874515}
  m_Layer: 9
  m_Name: Plank_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345403772874543
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403772874517}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 5.459999, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345403772874512
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403772874517}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345403772874513
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403772874517}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345403772874516
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403772874517}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345403772874515
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345403772874517}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000034332275}
--- !u!1 &940345404048152460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404048152452}
  - component: {fileID: 940345404048152453}
  - component: {fileID: 940345404048152454}
  - component: {fileID: 940345404048152456}
  - component: {fileID: 940345404048152457}
  - component: {fileID: 940345404048152458}
  - component: {fileID: 940345404048152459}
  m_Layer: 9
  m_Name: Connector_51
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404048152452
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 11.019997, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404048152453
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404048152454
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404048152456
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404048152457
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404048152458
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_ConnectedBody: {fileID: 940345403410411105}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.8878088}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404048152459
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404048152460}
  m_ConnectedBody: {fileID: 940345404863696312}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.22438584}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404073822072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404073822066}
  - component: {fileID: 940345404073822067}
  - component: {fileID: 940345404073822068}
  - component: {fileID: 940345404073822071}
  - component: {fileID: 940345404073822070}
  m_Layer: 9
  m_Name: Plank_9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404073822066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404073822072}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 19.239998, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345404073822067
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404073822072}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404073822068
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404073822072}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345404073822071
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404073822072}
  serializedVersion: 2
  m_Mass: 5
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 126
  m_CollisionDetection: 0
--- !u!65 &940345404073822070
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404073822072}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000030517578}
--- !u!1 &940345404147269258
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404147269250}
  - component: {fileID: 940345404147269251}
  - component: {fileID: 940345404147269252}
  - component: {fileID: 940345404147269254}
  - component: {fileID: 940345404147269255}
  - component: {fileID: 940345404147269256}
  - component: {fileID: 940345404147269257}
  m_Layer: 9
  m_Name: Connector_82
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404147269250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 18.049995, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404147269251
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404147269252
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404147269254
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404147269255
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404147269256
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_ConnectedBody: {fileID: 940345404668963662}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.9122}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404147269257
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404147269258}
  m_ConnectedBody: {fileID: 940345404073822071}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.25853622}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404166860152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404166860144}
  - component: {fileID: 940345404166860145}
  - component: {fileID: 940345404166860146}
  - component: {fileID: 940345404166860148}
  - component: {fileID: 940345404166860149}
  - component: {fileID: 940345404166860150}
  - component: {fileID: 940345404166860151}
  m_Layer: 9
  m_Name: Connector_21
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404166860144
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 4.3699875, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404166860145
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404166860146
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404166860148
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404166860149
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404166860150
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_ConnectedBody: {fileID: 940345404732579905}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.8780506}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404166860151
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404166860152}
  m_ConnectedBody: {fileID: 940345403772874516}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.20975974}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404188512407
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404188512431}
  - component: {fileID: 940345404188512400}
  - component: {fileID: 940345404188512401}
  - component: {fileID: 940345404188512403}
  - component: {fileID: 940345404188512404}
  - component: {fileID: 940345404188512405}
  - component: {fileID: 940345404188512406}
  m_Layer: 9
  m_Name: Connector_42
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404188512431
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 8.80999, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404188512400
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404188512401
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404188512403
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404188512404
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404188512405
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_ConnectedBody: {fileID: 940345404381596550}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.8878008}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404188512406
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404188512407}
  m_ConnectedBody: {fileID: 940345403410411105}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.19024384}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404270290551
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404270290447}
  - component: {fileID: 940345404270290544}
  - component: {fileID: 940345404270290545}
  - component: {fileID: 940345404270290547}
  - component: {fileID: 940345404270290548}
  - component: {fileID: 940345404270290549}
  - component: {fileID: 940345404270290550}
  m_Layer: 9
  m_Name: Connector_22
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404270290447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 4.3699875, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404270290544
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404270290545
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404270290547
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404270290548
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404270290549
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_ConnectedBody: {fileID: 940345404732579905}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.87805}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404270290550
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404270290551}
  m_ConnectedBody: {fileID: 940345403772874516}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.2097602}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404381596551
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404381596545}
  - component: {fileID: 940345404381596546}
  - component: {fileID: 940345404381596547}
  - component: {fileID: 940345404381596550}
  - component: {fileID: 940345404381596549}
  m_Layer: 9
  m_Name: Plank_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404381596545
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404381596551}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 7.6500015, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345404381596546
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404381596551}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404381596547
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404381596551}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345404381596550
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404381596551}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345404381596549
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404381596551}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000030517578}
--- !u!1 &940345404405794099
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404405794098}
  m_Layer: 9
  m_Name: (Prb)Bridge
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404405794098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404405794099}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 940345403689339918}
  - {fileID: 940345403289609756}
  - {fileID: 940345403353457515}
  - {fileID: 940345404732579906}
  - {fileID: 940345404166860144}
  - {fileID: 940345404270290447}
  - {fileID: 940345403772874543}
  - {fileID: 940345403371841169}
  - {fileID: 940345404956287400}
  - {fileID: 940345404381596545}
  - {fileID: 940345404458817009}
  - {fileID: 940345404188512431}
  - {fileID: 940345403410411132}
  - {fileID: 940345404048152452}
  - {fileID: 940345404529147463}
  - {fileID: 940345404863696307}
  - {fileID: 940345405172035082}
  - {fileID: 940345405007399825}
  - {fileID: 940345405052389639}
  - {fileID: 940345404964982860}
  - {fileID: 940345404464409947}
  - {fileID: 940345404668963657}
  - {fileID: 940345404862431958}
  - {fileID: 940345404147269250}
  - {fileID: 940345404073822066}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &940345404458817017
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404458817009}
  - component: {fileID: 940345404458817010}
  - component: {fileID: 940345404458817011}
  - component: {fileID: 940345404458817013}
  - component: {fileID: 940345404458817014}
  - component: {fileID: 940345404458817015}
  - component: {fileID: 940345404458817016}
  m_Layer: 9
  m_Name: Connector_41
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404458817009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 8.80999, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404458817010
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404458817011
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404458817013
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404458817014
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404458817015
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_ConnectedBody: {fileID: 940345404381596550}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.88780135}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404458817016
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404458817017}
  m_ConnectedBody: {fileID: 940345403410411105}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.19024338}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404464409923
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404464409947}
  - component: {fileID: 940345404464409948}
  - component: {fileID: 940345404464409949}
  - component: {fileID: 940345404464409951}
  - component: {fileID: 940345404464409920}
  - component: {fileID: 940345404464409921}
  - component: {fileID: 940345404464409922}
  m_Layer: 9
  m_Name: Connector_72
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404464409947
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 15.719994, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404464409948
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404464409949
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404464409951
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404464409920
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404464409921
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_ConnectedBody: {fileID: 940345405052389644}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.9219507}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404464409922
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404464409923}
  m_ConnectedBody: {fileID: 940345404668963662}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.22438632}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404529147471
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404529147463}
  - component: {fileID: 940345404529147464}
  - component: {fileID: 940345404529147465}
  - component: {fileID: 940345404529147467}
  - component: {fileID: 940345404529147468}
  - component: {fileID: 940345404529147469}
  - component: {fileID: 940345404529147470}
  m_Layer: 9
  m_Name: Connector_52
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404529147463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 11.019997, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404529147464
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404529147465
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404529147467
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404529147468
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404529147469
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_ConnectedBody: {fileID: 940345403410411105}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.88780826}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404529147470
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404529147471}
  m_ConnectedBody: {fileID: 940345404863696312}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.22438632}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404668963663
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404668963657}
  - component: {fileID: 940345404668963658}
  - component: {fileID: 940345404668963659}
  - component: {fileID: 940345404668963662}
  - component: {fileID: 940345404668963661}
  m_Layer: 9
  m_Name: Plank_8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404668963657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404668963663}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 16.839989, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345404668963658
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404668963663}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404668963659
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404668963663}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345404668963662
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404668963663}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345404668963661
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404668963663}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000034332275}
--- !u!1 &940345404732579911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404732579906}
  - component: {fileID: 940345404732579907}
  - component: {fileID: 940345404732579908}
  - component: {fileID: 940345404732579905}
  - component: {fileID: 940345404732579910}
  m_Layer: 9
  m_Name: Plank_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404732579906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404732579911}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 3.229988, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345404732579907
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404732579911}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404732579908
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404732579911}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345404732579905
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404732579911}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345404732579910
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404732579911}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000030517578}
--- !u!1 &940345404862431966
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404862431958}
  - component: {fileID: 940345404862431959}
  - component: {fileID: 940345404862431960}
  - component: {fileID: 940345404862431962}
  - component: {fileID: 940345404862431963}
  - component: {fileID: 940345404862431964}
  - component: {fileID: 940345404862431965}
  m_Layer: 9
  m_Name: Connector_81
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404862431958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 18.049995, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404862431959
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404862431960
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404862431962
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404862431963
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404862431964
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_ConnectedBody: {fileID: 940345404668963662}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.91220057}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404862431965
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404862431966}
  m_ConnectedBody: {fileID: 940345404073822071}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.25853577}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404863696313
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404863696307}
  - component: {fileID: 940345404863696308}
  - component: {fileID: 940345404863696309}
  - component: {fileID: 940345404863696312}
  - component: {fileID: 940345404863696311}
  m_Layer: 9
  m_Name: Plank_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404863696307
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404863696313}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 12.139992, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345404863696308
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404863696313}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404863696309
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404863696313}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345404863696312
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404863696313}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345404863696311
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404863696313}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000030517578}
--- !u!1 &940345404956287376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404956287400}
  - component: {fileID: 940345404956287401}
  - component: {fileID: 940345404956287402}
  - component: {fileID: 940345404956287404}
  - component: {fileID: 940345404956287405}
  - component: {fileID: 940345404956287406}
  - component: {fileID: 940345404956287407}
  m_Layer: 9
  m_Name: Connector_32
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404956287400
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 6.6299973, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404956287401
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404956287402
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404956287404
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404956287405
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404956287406
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_ConnectedBody: {fileID: 940345403772874516}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.8926836}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404956287407
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404956287376}
  m_ConnectedBody: {fileID: 940345404381596550}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.17561029}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345404964982964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345404964982860}
  - component: {fileID: 940345404964982861}
  - component: {fileID: 940345404964982862}
  - component: {fileID: 940345404964982960}
  - component: {fileID: 940345404964982961}
  - component: {fileID: 940345404964982962}
  - component: {fileID: 940345404964982963}
  m_Layer: 9
  m_Name: Connector_71
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345404964982860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 15.719994, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345404964982861
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345404964982862
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345404964982960
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345404964982961
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345404964982962
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_ConnectedBody: {fileID: 940345405052389644}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.9219513}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345404964982963
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345404964982964}
  m_ConnectedBody: {fileID: 940345404668963662}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.22438584}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345405007399833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345405007399825}
  - component: {fileID: 940345405007399826}
  - component: {fileID: 940345405007399827}
  - component: {fileID: 940345405007399829}
  - component: {fileID: 940345405007399830}
  - component: {fileID: 940345405007399831}
  - component: {fileID: 940345405007399832}
  m_Layer: 9
  m_Name: Connector_62
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345405007399825
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 13.32, y: 0.07999992, z: 3.2999573}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345405007399826
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345405007399827
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345405007399829
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345405007399830
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345405007399831
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_ConnectedBody: {fileID: 940345404863696312}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025237, y: 0.04123707, z: 0.89756644}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345405007399832
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405007399833}
  m_ConnectedBody: {fileID: 940345405052389644}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.40025234, y: 0.04123707, z: -0.24877805}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &940345405052389645
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345405052389639}
  - component: {fileID: 940345405052389640}
  - component: {fileID: 940345405052389641}
  - component: {fileID: 940345405052389644}
  - component: {fileID: 940345405052389643}
  m_Layer: 9
  m_Name: Plank_7
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345405052389639
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405052389645}
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 14.489998, y: 0.03999996, z: 0.1299591}
  m_LocalScale: {x: 7.92, y: 0.97, z: 2.05}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &940345405052389640
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405052389645}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345405052389641
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405052389645}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a83e62349221e8a4387de81b5430af6c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!54 &940345405052389644
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405052389645}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &940345405052389643
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405052389645}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.0000002, y: 1, z: 1.0000002}
  m_Center: {x: 0.0000038146973, y: 0, z: 0.000034332275}
--- !u!1 &940345405172035186
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 940345405172035082}
  - component: {fileID: 940345405172035083}
  - component: {fileID: 940345405172035084}
  - component: {fileID: 940345405172035086}
  - component: {fileID: 940345405172035087}
  - component: {fileID: 940345405172035184}
  - component: {fileID: 940345405172035185}
  m_Layer: 9
  m_Name: Connector_61
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &940345405172035082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_LocalRotation: {x: 0.7071068, y: 0.7071068, z: -0, w: 0}
  m_LocalPosition: {x: 13.32, y: 0.07999992, z: -3.0500336}
  m_LocalScale: {x: 0.60278994, y: 0.6599999, z: 0.6027899}
  m_Children: []
  m_Father: {fileID: 940345404405794098}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 90}
--- !u!33 &940345405172035083
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &940345405172035084
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e5be4bad6d8e29b4b8aa94631cef7eec, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!64 &940345405172035086
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!54 &940345405172035087
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &940345405172035184
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_ConnectedBody: {fileID: 940345404863696312}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151423, y: 0.04123707, z: 0.897567}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 10
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!59 &940345405172035185
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 940345405172035186}
  m_ConnectedBody: {fileID: 940345405052389644}
  m_Anchor: {x: 0, y: 1, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.40151426, y: 0.04123707, z: -0.24877758}
  m_UseSpring: 0
  m_Spring:
    spring: 2
    damper: 0
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 0
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
