using GPUAnimationCrowds; // For CharacterMovementState
using PlayerFAP.Authorings;
using PlayerFAP.Components; // For EnemyAnimationIDs
using ProjectDawn.Navigation; // For AgentBody
using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(Systems.PlayerFAP.EnemyMovementSystem))]
// Ensure this runs after agent movement systems update velocity
// Example: [UpdateAfter(typeof(AgentForceSystemGroup))] if using ProjectDawn's forces
public partial struct EnemyMovementAnimationSystem : ISystem
{
    // FIXED: Changed how we handle the ECB system
    private EntityQuery _ecbSystemQuery;
    private EndSimulationEntityCommandBufferSystem.Singleton _ecbSystem;
    
    [BurstCompile]
    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<CharacterMovementState>();
        state.RequireForUpdate<AgentBody>();
        
        // FIXED: Use RequireForUpdate for EndSimulationEntityCommandBufferSystem
        state.RequireForUpdate<EndSimulationEntityCommandBufferSystem.Singleton>();
    }

    private EntityCommandBuffer.ParallelWriter GetECommandBuffer(ref SystemState state)
    {
        var ecbSingleton = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>();
        return ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged).AsParallelWriter();
    }
    
    [BurstCompile]
    public void OnDestroy(ref SystemState state) { }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var job = new DetermineAnimationJob {ECB = GetECommandBuffer(ref state)};
        job.ScheduleParallel();
    }

    [BurstCompile]
    private partial struct DetermineAnimationJob : IJobEntity
    {
        // ParallelWriter for recording commands
        public EntityCommandBuffer.ParallelWriter ECB;
        
        private void Execute([ChunkIndexInQuery] int  chunkIndex, in Entity entity,ref CharacterMovementState movementState, in AgentBody agent)
        {
            float3 velocity = agent.Velocity;
            float speed = math.length(velocity);

            // Use the navigation system’s internal state: an agent is moving when it is **not** flagged as stopped.
            // This removes the need for per-type speed thresholds and guarantees consistent behaviour across all enemies.
            // Consider the agent moving only when its actual speed is above a small threshold.
            const float minMoveSpeed = 0.15f;
            bool isMoving = speed > minMoveSpeed;

            int targetAnimationID;

            // TODO: Add logic for Dead and Hit states, potentially by reading other components or flags in CharacterMovementState

            
            if (movementState.IsAttacking) // IsAttacking should be set by a combat system
            {
                targetAnimationID = (int)EnemyAnimationIDs.Attack;
            }
            else if (isMoving)
            {
                // Assuming runThreshold is available, e.g., from a shared settings component or fixed
                // For simplicity, using a const. Ideally, this comes from EnemyAuthoring settings.
                const float runSpeedThreshold = 2.0f; 
                if (speed > runSpeedThreshold)
                {
                    targetAnimationID = (int)EnemyAnimationIDs.Run;
                }
                else
                {
                    targetAnimationID = (int)EnemyAnimationIDs.Walk;
                }
            }
            else // Not attacking and not moving
            {
                targetAnimationID = (int)EnemyAnimationIDs.Idle;
            }

            // Update CharacterMovementState
            movementState.Speed = speed;
            movementState.IsMoving = isMoving;
            movementState.Direction = isMoving ? math.normalize(velocity) : movementState.Direction;

            // Create a new CharacterMovementState with updated values
            var updatedMovementState = new CharacterMovementState
            {
                Speed = speed,
                IsMoving = isMoving,
                Direction = isMoving ? math.normalize(velocity) : movementState.Direction,
                CurrentAnimationID = targetAnimationID,
                IsAttacking = movementState.IsAttacking // Preserve the existing value
                // Copy any other fields from the original movementState
            };

            // Only record a command if something has changed
            if (movementState.CurrentAnimationID != targetAnimationID || 
                !Mathf.Approximately(movementState.Speed, speed) || 
                movementState.IsMoving != isMoving ||
                (isMoving && !math.all(movementState.Direction == updatedMovementState.Direction)))
            {
                // Record a command to set the component
                ECB.SetComponent(chunkIndex, entity, updatedMovementState);
            }
            
            //Debug.Log($"[EnemyMovementAnimationSystem] :  {movementState.CurrentAnimationID}");
        }
    }
}
