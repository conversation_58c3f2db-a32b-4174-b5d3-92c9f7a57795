using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using DefaultNamespace.Mono.Interface;
using DG.Tweening;
using EditorTools;
using Events;
using Mono.Extensions;
using Unity.Mathematics;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public class MovementModule : MonoBehaviour, IModule<MovementSubState>, IUpdateSubModule<MovementSubState>,
        IUpdateState<AnimationSubState>
    {
        [Header("Configuration Reference")]
        [Tooltip("Reference to the MovementModuleConfiguration ScriptableObject")]
        public MovementModuleConfiguration movementConfig;

        public bool CanUpdate { get; private set; }

        [Header("Movement Values (Read from MovementModuleConfiguration)")]
        [Tooltip("Values read from MovementModuleConfiguration - do not modify directly")]
        [ReadOnly] [SerializeField] private bool improvedMovement;
        [ReadOnly] [SerializeField] private float accelerationTime;
        [ReadOnly] [SerializeField] private float decelerationTime;
        [ReadOnly] [SerializeField] private float aimingSpeedMultiplier;
        [ReadOnly] [SerializeField] private float strafingSpeedMultiplier;
        [ReadOnly] [SerializeField] private float backpedalSpeedMultiplier;
        [ReadOnly] [SerializeField] private AnimationCurve directionChangeCurve;

        private Vector3 currentVelocity = Vector3.zero;
        private Vector3 lastInputDirection = Vector3.zero;
        private float currentSpeed = 0f;
        private float velocityChangeSpeed = 0f;
        
        
        [field: SerializeField] public int ControllerIndex { get; private set; }

        [SerializeField] private bool m_useAnimancer;
        private string _stateName;
        private CancellationTokenSource _cancellationTokenSource;

        [field: SerializeField] public List<MainState> MainState { get; private set; }

        [SerializeField] private MovementSubState _moduleSubState;

        public MovementSubState SubState
        {
            get { return _moduleSubState; }
            set
            {
                _moduleSubState = value;
                PlayerController.Instance.StateManager.CurrentSubState = _moduleSubState;
            }
        }

        public void SetState()
        {
            throw new NotImplementedException();
        }

        // --- Force Stop and Stand for Target Loss Recovery ---
        public void ForceStopAndStand()
        {
            Debug.Log("[MovementModule] Forcing Stop -> Standing due to target loss.");
            IsMoving = false;
            SubState = MovementSubState.Stop;
            ToStop();
            // After stop, transition to standing
            StartCoroutine(WaitAndSetStanding());
        }
        private IEnumerator WaitAndSetStanding()
        {
            yield return new WaitForSeconds(0.15f);
            SubState = MovementSubState.Standing;
            Debug.Log("[MovementModule] Transitioned to Standing after Stop.");
        }

        AnimationSubState IUpdateState<AnimationSubState>.SubState
        {
            get { return _subState; }
            set { _subState = value; }
        }

        public void SetSubModule()
        {
            PlayerController.Instance.StateManager.CurrentSubState = _moduleSubState;
        }

        [field: SerializeField] public bool HasRefreshRate { get; private set; }
        [field: SerializeField] public float RefreshRate { get; private set; }

        public Enum GetModuleSubState() => SubState;

        [SerializeField] private CharacterAnimationModule animationModule;
        [SerializeField] private CharacterParameters characterParameters;

        public bool IsMoving;
        [SerializeField] private AnimationSubState _subState;

        // Public properties for physics integration validation
        public bool IsGrounded => m_isGrounded;
        public Vector3 CurrentVelocity => m_rigidbody != null ? m_rigidbody.linearVelocity : Vector3.zero;
        public float CurrentHorizontalSpeed => currentSpeed; // X/Z movement speed
        public float CurrentVerticalVelocity => m_rigidbody != null ? m_rigidbody.linearVelocity.y : 0f; // Y physics velocity
        public bool HasRigidbody => m_rigidbody != null;

        public float InputAngleDump;
        private float lastInputAngle;
        [SerializeField] private float inputAngle;

        [Header("Speed and Rotation Values (Read from MovementModuleConfiguration)")]
        [Tooltip("Values read from MovementModuleConfiguration - do not modify directly")]
        [ReadOnly] [SerializeField] private float moveSpeed;
        [ReadOnly] [SerializeField] private float rotationSpeed;

        // Character rotation parameters for target-based rotation
        [Header("Target Rotation Parameters (Read from MovementModuleConfiguration)")]
        [Tooltip("Values read from MovementModuleConfiguration - do not modify directly")]
        [ReadOnly] [SerializeField] private float m_minRotationAngle;
        [ReadOnly] [SerializeField] private bool m_useWholeBodyRotation;
        [ReadOnly] [SerializeField] private float m_rotationSmoothTime;
        private float m_currentRotationVelocity; // For SmoothDamp

        [SerializeField] private float angleDifference;

        [SerializeField] private Transform m_playerTransform;
        [SerializeField] private bool m_changeDirection;
        [SerializeField] private bool m_waitForChangeDirection;

        // Physics-based movement components
        [SerializeField] private Rigidbody m_rigidbody;
        [SerializeField] private Collider characterCollider; 
        private Vector3 m_currentVelocity = Vector3.zero; // For SmoothDamp velocity smoothing
        private Vector3 m_targetVelocity = Vector3.zero;  // Target velocity for smooth movement

        // Cache movement data for FixedUpdate
        private Vector3 m_cachedMovementVector = Vector3.zero;
        private bool m_shouldApplyMovement = false;

        // Ground detection and gravity for physics
        [Header("Ground Detection & Gravity (Read from MovementModuleConfiguration)")]
        [Tooltip("Values read from MovementModuleConfiguration - do not modify directly")]
        [SerializeField] private LayerMask groundMask = -1;
        [ReadOnly] [SerializeField] private float groundCheckDistance = 0.1f;
        [ReadOnly] [SerializeField] private float gravityMultiplier = 6f;
        private bool m_isGrounded = false;

        // Gravity direction vectors (simplified from CharacterManager)
        private Vector3 m_globalDown = Vector3.down;

        private Coroutine m_waitforStopCorutine;
        [SerializeField] private bool m_isTurningBack;
        [SerializeField] private bool _isTransitioning;
        private Queue<(string name, Action action)> _pendingActions = new Queue<(string name, Action action)>();
        private Tweener rotationTweener; // Store the rotation tween for cancellation if needed

        [Header("Transition Parameters (Read from MovementModuleConfiguration)")]
        [Tooltip("Values read from MovementModuleConfiguration - do not modify directly")]
        [ReadOnly] [SerializeField] private float fastRotationSpeed;
        [ReadOnly] [SerializeField] private float m_waitForStop;
        [ReadOnly] [SerializeField] private float _transitionCooldown;
        //[ReadOnly] [SerializeField] private float _improvedTransitionCooldown;

        public void Initialize(int controllerIndex)
        {
            // Update values from configuration
            UpdateValuesFromConfiguration();

            animationModule = GetComponent<CharacterAnimationModule>();
            animationModule.Initialize(m_useAnimancer);
            animationModule.States[MovementSubState.Standing].OnEnterState(0.25f);
            lastInputAngle = characterParameters.InputAngle.Value; // Initialize lastInputAngle
            EventManager.Subscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Subscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
        }

        private void OnValidate()
        {
            UpdateValuesFromConfiguration();
        }

        private void UpdateValuesFromConfiguration()
        {
            if (movementConfig != null)
            {
                // Movement Improvement
                improvedMovement = movementConfig.improvedMovement;
                // Movement Parameters
                accelerationTime = movementConfig.accelerationTime;
                decelerationTime = movementConfig.decelerationTime;
                aimingSpeedMultiplier = movementConfig.aimingSpeedMultiplier;
                strafingSpeedMultiplier = movementConfig.strafingSpeedMultiplier;
                backpedalSpeedMultiplier = movementConfig.backpedalSpeedMultiplier;
                directionChangeCurve = movementConfig.directionChangeCurve;

                // Speed and Rotation
                moveSpeed = movementConfig.moveSpeed;
                rotationSpeed = movementConfig.rotationSpeed;

                // Target Rotation Parameters
                m_minRotationAngle = movementConfig.minRotationAngle;
                m_useWholeBodyRotation = movementConfig.useWholeBodyRotation;
                m_rotationSmoothTime = movementConfig.rotationSmoothTime;

                // Transition Parameters
                fastRotationSpeed = movementConfig.fastRotationSpeed;
                m_waitForStop = movementConfig.waitForStop;
                _transitionCooldown = movementConfig.transitionCooldown;
            }
        }

        private void Start()
        {
            if (m_playerTransform == null)
            {
                m_playerTransform = transform;
            }

            // Initialize Rigidbody component
            m_rigidbody = GetComponent<Rigidbody>();
            if (m_rigidbody == null)
            {
                Debug.LogError("[MovementModule] Rigidbody component not found! Physics-based movement requires a Rigidbody component.");
            }
            else
            {
                // Configure rigidbody for hybrid movement (transform X/Z, physics Y)
                m_rigidbody.freezeRotation = true; // Prevent physics rotation interference

                // Don't freeze position - we'll handle X/Z manually in FixedUpdate
                m_rigidbody.constraints = RigidbodyConstraints.FreezeRotation;
            }
        }

        private void FixedUpdate()
        {
            // Handle physics-based movement in FixedUpdate for proper physics timing
            if (m_rigidbody != null)
            {
                // Apply cached X/Z movement from Update
                if (m_shouldApplyMovement && m_cachedMovementVector.magnitude > 0)
                {
                    Vector3 oldPosition = m_playerTransform.position;
                    m_playerTransform.position += m_cachedMovementVector;
                    Debug.Log($"[MovementModule] Applied movement: {m_cachedMovementVector}, Old pos: {oldPosition}, New pos: {m_playerTransform.position}");
                }
                else if (m_shouldApplyMovement)
                {
                    Debug.Log($"[MovementModule] Movement blocked - ShouldApply: {m_shouldApplyMovement}, CachedVector: {m_cachedMovementVector}");
                }

                // Reset X/Z velocity to prevent physics interference with transform movement
                // but preserve Y velocity for gravity
                Vector3 currentVelocity = m_rigidbody.linearVelocity;
                m_rigidbody.linearVelocity = new Vector3(0, currentVelocity.y, 0);

                // Apply proper gravity force (like CharacterManager) - only affects Y-axis
                ApplyGravity();
            }

            // Reset movement flags
            m_shouldApplyMovement = false;
            m_cachedMovementVector = Vector3.zero;
        }

        private void OnDestroy()
        {
            EventManager.Unsubscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Unsubscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);

            // Kill any active tweens
            if (rotationTweener != null && rotationTweener.IsActive())
            {
                rotationTweener.Kill();
            }
        }

        private void OnRequireFastRotation(OnRequireFastRotationEvent evt)
        {
            // Get the target position
            Vector3 targetPosition = evt.TargetPosition;

            // Calculate direction to target (ignore Y axis for horizontal rotation)
            Vector3 directionToTarget = targetPosition - m_playerTransform.position;
            directionToTarget.y = 0; // Keep rotation on horizontal plane

            if (directionToTarget.magnitude < 0.1f)
                return; // Avoid rotating to very close positions

            directionToTarget.Normalize();

            // Calculate target rotation
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);

            // Kill any existing rotation tween
            if (rotationTweener != null && rotationTweener.IsActive())
            {
                rotationTweener.Kill();
            }

            // Apply rotation with DOTween for smooth but quick rotation
            rotationTweener = m_playerTransform.DORotateQuaternion(targetRotation, fastRotationSpeed)
                .SetEase(Ease.OutQuint); // Fast start, smooth finish

            Debug.Log($"Fast rotation applied towards target at {targetPosition}");
        }

        /// <summary>
        /// Smoothly rotates the entire character to face the current target
        /// </summary>
        private void UpdateCharacterRotation()
        {
            if (characterParameters == null || m_playerTransform == null) return;
            
            // Get target from CharacterParameters
            float3 targetPosition = characterParameters.AimTarget;
            
            // Skip if no valid target
            if (targetPosition.Equals(float3.zero)) return;
            
            // Get direction to target (ignore Y axis for horizontal rotation)
            Vector3 directionToTarget = (Vector3)targetPosition - m_playerTransform.position;
            directionToTarget.y = 0; // Keep rotation on horizontal plane
            
            if (directionToTarget.magnitude < 0.1f) return; // Avoid rotating to very close positions
            
            // Calculate angle between forward direction and target direction
            float targetAngle = Mathf.Atan2(directionToTarget.x, directionToTarget.z) * Mathf.Rad2Deg;
            float currentAngle = m_playerTransform.eulerAngles.y;
            
            // Calculate the shortest angle difference (accounting for 360 degree wrapping)
            float angleDifference = Mathf.DeltaAngle(currentAngle, targetAngle);
            
            // Only rotate the whole body if the angle difference is significant
            if (Mathf.Abs(angleDifference) > m_minRotationAngle)
            {
                // Use SmoothDampAngle for smooth rotation with acceleration/deceleration
                float newAngle = Mathf.SmoothDampAngle(
                    currentAngle, 
                    targetAngle, 
                    ref m_currentRotationVelocity, 
                    m_rotationSmoothTime);
                
                // Apply the rotation
                m_playerTransform.rotation = Quaternion.Euler(0, newAngle, 0);
                
                DebugLogManager.Instance.Log($"[MovementModule] Rotating character to face target. Angle diff: {angleDifference:F1}°");
            }
        }

        private void OnChangeDirection(OnChangeDirectionEvent onChangeDirectionEvent)
        {
            // Remove speed check for aim-to-normal transitions
            if (!m_waitForChangeDirection && !characterParameters.WantsToRotateBehindTarget &&
                (characterParameters.CurrentPlayerSpeed.Value > 0.1f || onChangeDirectionEvent.IsAimToNormal))
            {
                if (improvedMovement && !onChangeDirectionEvent.IsAimToNormal)
                {
                    // For improved movement, handle direction changes differently based on angle
                    float angle = Mathf.Abs(characterParameters.InputAngle.Value);
        
                    if (angle > 135f)
                    {
                        // Full stop and direction change for extreme angles
                        DebugLogManager.Instance.Log("Direction change triggered (extreme angle: " + angle + ")");
                        m_waitForChangeDirection = true;
                        IsMoving = false;
                        SubState = MovementSubState.Stop;
                        m_changeDirection = true;
                        ToStop(onChangeDirectionEvent.IsAimToNormal);
                        m_changeDirection = false;
                    }
                    else if (angle > 90f)
                    {
                        // Just slow down for moderate angles
                        DebugLogManager.Instance.Log("Direction change slowing down (moderate angle: " + angle + ")");
                        // Don't stop, just transition to walking with turn
                        if (SubState != MovementSubState.WalkingWithTurn)
                        {
                            SubState = MovementSubState.WalkingWithTurn;
                        }
        
                        // Set a shorter wait time for direction changes
                        m_waitForChangeDirection = true;
                        this.WaitForSecondsAsync("DirectionChange", 0.2f, () => { m_waitForChangeDirection = false; },
                            new System.Threading.CancellationToken());
                    }
                }
                else
                {
                    // Original direction change code
                    DebugLogManager.Instance.Log("Direction change triggered");
                    m_waitForChangeDirection = true;
                    IsMoving = false;
                    SubState = MovementSubState.Stop;
                    m_changeDirection = true;
                    ToStop(onChangeDirectionEvent.IsAimToNormal);
                    m_changeDirection = false;
                }
            }
        }

        private void OnCharacterAnimationFinished(OnCharacterAnimationFinishedEvent evt)
        {
            if (evt.MovementSubState == MovementSubState.Stop)
            {
                m_waitForStop = evt.AnimationLength;
            }
        }

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if (animationModule == null) return;

            // Update ground detection for physics-based movement
            CheckGrounded();

            SubState = (MovementSubState) currentSubState;

            // ALWAYS apply movement regardless of animation state transitions
            ApplyMovement(characterParameters.Horizontal.Value, characterParameters.Vertical.Value);

            // Only process animation state changes if not transitioning
            if (!CanProcessInput())
                return;

            float currentInputAngle = characterParameters.InputAngle.Value;

            // if (!m_AimingStatus)
            //     angleDifference = Mathf.Abs(characterParameters.InputAngle.Value);
            // else
            //     angleDifference = Mathf.Abs(Mathf.DeltaAngle(lastInputAngle, currentInputAngle));

            if (!m_useAnimancer)
                animationModule.SetFloat("InputAngle", characterParameters.InputAngle.Value, 0.15f, Time.deltaTime);

            if (m_changeDirection)
            {
                return;
            }
            
            // Apply whole body rotation if needed
            if (characterParameters.IsAiming && m_useWholeBodyRotation)
            {
                UpdateCharacterRotation();
            }

            if (SubState == MovementSubState.Standing)
            {
                if (m_useAnimancer)
                    animationModule.PlayAnimationState(MovementSubState.Standing, 0.25f);
                else
                {
                    animationModule.SetFloat("InputMagnitude", 0, 0.0f, 0.0f);
                    animationModule.SetFloat("SprintFactor", 0, 0.0f, 0.0f);
                }
            }

            if (characterParameters.InputMagnitude.Value > 0.2f && !IsMoving && SubState == MovementSubState.Standing)
            {
                if (!m_useAnimancer)
                    SetWalkStartAngle();

                IsMoving = true;

                CancelPreviousTask("Standing");
                _cancellationTokenSource = new CancellationTokenSource();
                _stateName = "Standing";
                this.WaitForSecondsAsync("Standing", 0.04f,
                    () => { SubState = MovementSubState.WalkingStart; }
                    , _cancellationTokenSource.Token);
            }
            else if (characterParameters.InputMagnitude.Value > 0.2f && IsMoving &&
                     SubState == MovementSubState.WalkingStart)
            {
                SubState = MovementSubState.WalkingStart;
                if (m_useAnimancer)
                    animationModule.PlayAnimationState(MovementSubState.WalkingStart, 0.25f);
                WalkingStart(characterParameters.Horizontal.Value, characterParameters.Vertical.Value);
            }
            else if (characterParameters.InputMagnitude.Value > 0.2f && IsMoving &&
                     SubState == MovementSubState.WalkingWithTurn)
            {
                WalkingWithTurning(characterParameters.Horizontal.Value, characterParameters.Vertical.Value);
            }
            else if (characterParameters.InputMagnitude.Value < 0.2f && SubState != MovementSubState.Standing &&
                     SubState != MovementSubState.Stop)
            {
                IsMoving = false;
                SubState = MovementSubState.Stop;
                ToStop();
            }

            PlayerController.Instance.StateManager.CurrentSubState = SubState;

            lastInputAngle = currentInputAngle; // Update lastInputAngle
        }

        private void SetWalkStartAngle()
        {
            float currentMagnitude = animationModule.IsPlayingAnimation ? 1f : 0f;
            if (currentMagnitude == 0)
            {
                animationModule.SetFloat("WalkStartAngle", characterParameters.InputAngle.Value);
            }
        }

        private void WalkingStart(float horizontal, float vertical)
        {
            if (!m_useAnimancer)
            {
                animationModule.SetBool("IsStopLU", false);
                animationModule.SetBool("IsStopRU", false);
                animationModule.SetFloat("Horizontal", horizontal);
                animationModule.SetFloat("Vertical", vertical);
                animationModule.SetFloat("InputMagnitude", characterParameters.InputMagnitude.Value, 0.12f,
                    Time.deltaTime);
                animationModule.SetFloat("InputAngle", characterParameters.InputAngle.Value);
            }

            CancelPreviousTask("WalkStart");
            _cancellationTokenSource = new CancellationTokenSource();
            _stateName = "WalkStart";
            this.WaitForSecondsAsync("WalkStart", 1, () =>
            {
                if (SubState == MovementSubState.Stop)
                    return;
                if (m_useAnimancer)
                    animationModule.PlayAnimationState(MovementSubState.WalkingWithTurn, 0.25f);

                if (m_isTurningBack)
                {
                    m_isTurningBack = false;
                }
                SubState = MovementSubState.WalkingWithTurn;
                m_waitForChangeDirection = false;
            }, _cancellationTokenSource.Token);
        }

        private void WalkingWithTurning(float horizontal, float vertical)
        {
            if (!m_useAnimancer)
            {
                if (improvedMovement)
                {
                    // Calculate angle difference between current and target angle
                    float angleDifference = Mathf.Abs(Mathf.DeltaAngle(inputAngle, characterParameters.InputAngle.Value));

                    // Adjust smoothing based on angle difference
                    // Faster smoothing for small changes, slower for large changes
                    float smoothingFactor = Mathf.Lerp(5f, 2f, angleDifference / 180f);

                    // Adjust dampTime based on angle difference
                    float dampTime = Mathf.Lerp(0.1f, 0.25f, angleDifference / 180f);

                    animationModule.SetFloat("Horizontal", horizontal, dampTime, Time.deltaTime);
                    animationModule.SetFloat("Vertical", vertical, dampTime, Time.deltaTime);
                    animationModule.SetFloat("InputMagnitude", characterParameters.InputMagnitude.Value, dampTime, Time.deltaTime);

                    // Apply smoothing to input angle using LerpAngle to handle angle wrapping
                    inputAngle = Mathf.LerpAngle(inputAngle, characterParameters.InputAngle.Value, Time.deltaTime * smoothingFactor);
                    animationModule.SetFloat("InputAngle", inputAngle);
                }
                else
                {
                    // Original code
                    InputAngleDump = Mathf.Lerp(3, .8f, .8f);
                    animationModule.SetFloat("Horizontal", horizontal, 0.25f, Time.deltaTime);
                    animationModule.SetFloat("Vertical", vertical, 0.25f, Time.deltaTime);
                    animationModule.SetFloat("InputMagnitude", characterParameters.InputMagnitude.Value, 0.2f,
                        Time.deltaTime);
                    inputAngle = Mathf.Lerp(inputAngle, characterParameters.InputAngle.Value,
                        Time.deltaTime * InputAngleDump);
                    animationModule.SetFloat("InputAngle", inputAngle);
                }
            }
        }

        private void ToStop(bool aimToNormal = false)
        {
            if (!m_useAnimancer)
            {
                float ru = animationModule.GetFloat("IsRU");
                if (ru <= 0.95f)
                {
                    animationModule.SetBool("IsStopLU", true);
                    animationModule.SetBool("IsStopRU", false);
                }
                else
                {
                    animationModule.SetBool("IsStopLU", false);
                    animationModule.SetBool("IsStopRU", true);
                }

                animationModule.SetFloat("InputMagnitude", 0, 0.2f, Time.deltaTime);
            }

            if (!characterParameters.IsStopping)
            {
                characterParameters.IsStopping = true;
                if (!m_useAnimancer)
                    animationModule.SetFloat("WalkStopAngle", characterParameters.StopWalkAngle.Value);
                else
                    animationModule.PlayAnimationState(MovementSubState.Stop, 0.25f,
                        aimToNormal ? new {AnimName = "AimToNormal"} : null);

                CancelPreviousTask("Stop");

                _cancellationTokenSource = new CancellationTokenSource();
                _stateName = "Stop";

                if (!aimToNormal)
                {
                    if (m_waitforStopCorutine != null)
                        StopCoroutine(m_waitforStopCorutine);
                    m_waitforStopCorutine = StartCoroutine(WaitForStop());
                }
                else
                {
                }

                this.WaitForSecondsAsync("Stop", m_waitForStop, () =>
                {
                    if (aimToNormal)
                    {
                        SubState = MovementSubState.Standing;
                        characterParameters.IsStopping = false;
                        m_isTurningBack = true;
                        //GameManager.Instance.moduleManager.EnableModule<AimingModule>();
                        return;
                    }
                }, _cancellationTokenSource.Token);
            }
        }

        private IEnumerator WaitForStop()
        {
            yield return new WaitUntil(() => !characterParameters.IsStopping);
            SubState = MovementSubState.Standing;
            characterParameters.IsStopping = false;
        }

        private void CancelPreviousTask(string calledName, Action callBeforeCancel = null)
        {
            if (_stateName == calledName)
                return;

            if (_isTransitioning)
            {
                // Queue the action if we're in transition
                _pendingActions.Enqueue((calledName, callBeforeCancel));
                return;
            }

            _isTransitioning = true;

            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }

            _cancellationTokenSource = new CancellationTokenSource();

            // Start transition cooldown
            this.WaitForSecondsAsync("TransitionCooldown", _transitionCooldown, () =>
            {
                _isTransitioning = false;

                // Process any pending actions
                if (_pendingActions.Count > 0)
                {
                    var nextAction = _pendingActions.Dequeue();
                    nextAction.action?.Invoke();
                }
            }, _cancellationTokenSource.Token);
        }

        public bool CanProcessInput()
        {
            return !_isTransitioning;
        }

        /// <summary>
        /// Check if the character is grounded using physics raycast
        /// Simplified version inspired by CharacterManager's CheckGrounded method
        /// </summary>
        private void CheckGrounded()
        {
            if (m_rigidbody == null) return;

            // Get the collider bounds for ground checking
            Collider characterCollider = GetComponent<Collider>();
            if (characterCollider == null) return;

            // Calculate ground check position (bottom of character)
            Vector3 groundCheckPos = transform.position - new Vector3(0, characterCollider.bounds.extents.y, 0);

            // Perform ground check using sphere cast
            m_isGrounded = Physics.CheckSphere(groundCheckPos, groundCheckDistance, groundMask);
        }

        /// <summary>
        /// Apply gravity force to rigidbody - based on CharacterManager's ApplyGravity method
        /// This ensures proper physics-based gravity that works during movement
        /// </summary>
        private void ApplyGravity()
        {
            if (m_rigidbody == null) return;

            // Calculate gravity force similar to CharacterManager
            Vector3 gravity = m_globalDown * gravityMultiplier * -Physics.gravity.y;

            // Apply gravity force to rigidbody
            m_rigidbody.AddForce(gravity);
        }

        private void ApplyMovement(float horizontalInput, float verticalInput)
        {
            Vector3 inputDirection = new Vector3(horizontalInput, 0, verticalInput);
            float inputMagnitude = inputDirection.magnitude;

            // Normalize only if magnitude > 1 to preserve partial inputs
            if (inputMagnitude > 1f)
                inputDirection.Normalize();
            else if (inputMagnitude < 0.01f)
                inputDirection = Vector3.zero;

            // Calculate target speed with modifiers
            float targetSpeed = moveSpeed;
            bool isAiming = characterParameters.IsAiming;

            // Apply speed modifiers
            if (isAiming)
                targetSpeed *= aimingSpeedMultiplier;

            // Apply strafing and backpedal modifiers
            if (isAiming && Mathf.Abs(horizontalInput) > 0.7f)
                targetSpeed *= strafingSpeedMultiplier; // Strafe modifier

            if (isAiming && verticalInput < -0.3f)
                targetSpeed *= backpedalSpeedMultiplier; // Backpedal modifier

            // Calculate acceleration/deceleration time
            float accelerationRate = inputMagnitude > 0.01f ? accelerationTime : decelerationTime;

            // Calculate direction change factor (slower when changing direction drastically)
            float directionChangeFactor = 1f;
            if (lastInputDirection.magnitude > 0.1f && inputDirection.magnitude > 0.1f)
            {
                float directionDot = Vector3.Dot(lastInputDirection.normalized, inputDirection.normalized);
                directionChangeFactor = directionChangeCurve.Evaluate((directionDot + 1f) * 0.5f);
            }

            // Apply smooth acceleration/deceleration for X/Z movement (original logic)
            currentSpeed = Mathf.SmoothDamp(currentSpeed, targetSpeed * inputMagnitude,
                ref velocityChangeSpeed, accelerationRate * (1f / directionChangeFactor));

            // Calculate movement vector for X/Z axes only
            Vector3 movementVector = inputDirection * currentSpeed * Time.deltaTime;

            // Apply movement - BYPASS animation state restrictions for physics-based movement
            if (movementVector.magnitude > 0 && inputMagnitude > 0.01f)
            {
                // Cache movement vector for FixedUpdate
                m_cachedMovementVector = movementVector;
                m_shouldApplyMovement = true;

                // Debug logging
                Debug.Log($"[MovementModule] Caching movement: {movementVector}, Speed: {currentSpeed}, SubState: {SubState}, InputMag: {inputMagnitude}");

                // Only apply normal rotation if we're not in the middle of a fast rotation
                // and not aiming (aiming should prioritize looking at targets)
                if ((rotationTweener == null || !rotationTweener.IsActive()) && !isAiming)
                {
                    // Calculate rotation speed based on input magnitude (faster rotation at higher speeds)
                    float adaptiveRotationSpeed = rotationSpeed * Mathf.Lerp(0.5f, 1.0f, inputMagnitude);

                    // Rotate the character with smooth interpolation
                    Quaternion targetRotation = Quaternion.LookRotation(inputDirection);
                    m_playerTransform.rotation = Quaternion.RotateTowards(
                        m_playerTransform.rotation,
                        targetRotation,
                        adaptiveRotationSpeed * Time.deltaTime
                    );
                }
            }
            else
            {
                // No movement to cache
                m_shouldApplyMovement = false;
                m_cachedMovementVector = Vector3.zero;
            }

            // Store last direction for next frame
            if (inputDirection.magnitude > 0.1f)
                lastInputDirection = inputDirection;

            // Update character parameters with current speed for animations
            characterParameters.CurrentPlayerSpeed.Value = currentSpeed / moveSpeed; // Normalized speed
          }

        /// <summary>
        /// Validates that the hybrid physics integration is working correctly
        /// X/Z movement via transform, Y-axis via rigidbody physics
        /// </summary>
        public bool ValidatePhysicsIntegration()
        {
            if (m_rigidbody == null)
            {
                Debug.LogError("[MovementModule] Physics validation failed: Rigidbody component is missing!");
                return false;
            }

            if (movementConfig == null)
            {
                Debug.LogError("[MovementModule] Physics validation failed: MovementModuleConfiguration is missing!");
                return false;
            }

            if (characterParameters == null)
            {
                Debug.LogError("[MovementModule] Physics validation failed: CharacterParameters is missing!");
                return false;
            }

            Debug.Log("[MovementModule] Hybrid physics integration validation passed successfully!");
            Debug.Log($"[MovementModule] X/Z Movement: Transform-based, Y Movement: Physics-based");
            Debug.Log($"[MovementModule] IsGrounded: {m_isGrounded}, Horizontal Speed: {CurrentHorizontalSpeed:F2}, Vertical Velocity: {CurrentVerticalVelocity:F2}");
            return true;
        }
    }
}